"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  QrCode,
  Wifi,
  Upload,
  Download,
  Eye,
  EyeOff,
  Info,
  Network,
} from "lucide-react";
import { toast } from "sonner";
import QRCode from "qrcode";

interface QRState {
  wifiSSID: string;
  wifiPassword: string;
  showPassword: boolean;
  wifiQRCode: string;
  uploadQRCode: string;
  isGenerating: boolean;
  error: string;
  detectedIP: string;
  customIP: string;
  useCustomIP: boolean;
}

export default function QRAccessPage() {
  const [state, setState] = useState<QRState>({
    wifiSSID: "",
    wifiPassword: "",
    showPassword: false,
    wifiQRCode: "",
    uploadQRCode: "",
    isGenerating: false,
    error: "",
    detectedIP: "",
    customIP: "",
    useCustomIP: false,
  });

  // Detect local IP address on component mount
  useEffect(() => {
    const detectLocalIP = async () => {
      try {
        // Try to get the local IP by creating a WebRTC connection
        const pc = new RTCPeerConnection({
          iceServers: [{ urls: "stun:stun.l.google.com:19302" }],
        });

        pc.createDataChannel("");
        const offer = await pc.createOffer();
        await pc.setLocalDescription(offer);

        return new Promise<string>((resolve) => {
          pc.onicecandidate = (event) => {
            if (event.candidate) {
              const candidate = event.candidate.candidate;
              const ipMatch = candidate.match(/(\d+\.\d+\.\d+\.\d+)/);
              if (ipMatch && !ipMatch[1].startsWith("127.")) {
                pc.close();
                resolve(ipMatch[1]);
              }
            }
          };

          // Fallback to window.location.hostname after 3 seconds
          setTimeout(() => {
            pc.close();
            resolve(window.location.hostname);
          }, 3000);
        });
      } catch (error) {
        console.warn("Could not detect local IP, using hostname:", error);
        return window.location.hostname;
      }
    };

    detectLocalIP().then((ip) => {
      setState((prev) => ({ ...prev, detectedIP: ip }));
    });
  }, []);

  const generateQRCodes = async () => {
    if (!state.wifiSSID.trim()) {
      setState((prev) => ({ ...prev, error: "WiFi network name is required" }));
      return;
    }

    setState((prev) => ({ ...prev, isGenerating: true, error: "" }));

    try {
      // Generate WiFi QR Code
      // WiFi QR format: WIFI:T:WPA;S:SSID;P:password;H:false;; (for WPA/WPA2)
      // WiFi QR format: WIFI:T:nopass;S:SSID;P:;H:false;; (for open networks)
      const securityType = state.wifiPassword.trim() ? "WPA" : "nopass";
      const wifiString = `WIFI:T:${securityType};S:${state.wifiSSID};P:${state.wifiPassword};H:false;;`;
      const wifiQRDataURL = await QRCode.toDataURL(wifiString, {
        width: 256,
        margin: 2,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
      });

      // Generate Upload Document QR Code using custom IP or detected IP
      const protocol = window.location.protocol;
      const port = window.location.port ? `:${window.location.port}` : "";
      const baseURL =
        state.useCustomIP && state.customIP.trim()
          ? state.customIP.trim()
          : state.detectedIP || window.location.hostname;
      const uploadURL = `${protocol}//${baseURL}${port}/upload`;

      const uploadQRDataURL = await QRCode.toDataURL(uploadURL, {
        width: 256,
        margin: 2,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
      });

      setState((prev) => ({
        ...prev,
        wifiQRCode: wifiQRDataURL,
        uploadQRCode: uploadQRDataURL,
        isGenerating: false,
      }));

      toast.success("QR codes generated successfully!");
    } catch (error) {
      console.error("Error generating QR codes:", error);
      setState((prev) => ({
        ...prev,
        error: "Failed to generate QR codes. Please try again.",
        isGenerating: false,
      }));
      toast.error("Failed to generate QR codes");
    }
  };

  const downloadQRCode = (dataURL: string, filename: string) => {
    try {
      const link = document.createElement("a");
      link.download = filename;
      link.href = dataURL;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.success(`${filename} downloaded successfully!`);
    } catch (error) {
      console.error("Error downloading QR code:", error);
      toast.error("Failed to download QR code");
    }
  };

  const clearQRCodes = () => {
    setState((prev) => ({
      ...prev,
      wifiQRCode: "",
      uploadQRCode: "",
      error: "",
    }));
    toast.success("QR codes cleared");
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    generateQRCodes();
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-3 mb-6">
        <QrCode className="h-8 w-8 text-primary" />
        <div>
          <h1 className="text-3xl font-bold">QR Access Generator</h1>
          <p className="text-muted-foreground">
            Generate QR codes for WiFi connection and document upload access
          </p>
        </div>
      </div>

      {state.error && (
        <Alert variant="destructive">
          <AlertDescription>{state.error}</AlertDescription>
        </Alert>
      )}

      {/* Information Alert */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>How to use:</strong> Generate QR codes for easy access. The
          WiFi QR code allows automatic connection to your network, while the
          Upload QR code provides direct access to the document upload page.
          {state.detectedIP && (
            <>
              <br />
              <strong>Upload URL:</strong> {window.location.protocol}//
              {state.detectedIP}
              {window.location.port ? `:${window.location.port}` : ""}/upload
            </>
          )}
        </AlertDescription>
      </Alert>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* Input Form */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Wifi className="h-5 w-5" />
              WiFi Connection Details
            </CardTitle>
            <CardDescription>
              Enter the WiFi network details to generate a connection QR code
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="wifiSSID">Network Name (SSID) *</Label>
                <Input
                  id="wifiSSID"
                  type="text"
                  placeholder="Enter WiFi network name"
                  value={state.wifiSSID}
                  onChange={(e) =>
                    setState((prev) => ({ ...prev, wifiSSID: e.target.value }))
                  }
                  disabled={state.isGenerating}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="wifiPassword">Password (optional)</Label>
                <div className="relative">
                  <Input
                    id="wifiPassword"
                    type={state.showPassword ? "text" : "password"}
                    placeholder="Enter WiFi password"
                    value={state.wifiPassword}
                    onChange={(e) =>
                      setState((prev) => ({
                        ...prev,
                        wifiPassword: e.target.value,
                      }))
                    }
                    disabled={state.isGenerating}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() =>
                      setState((prev) => ({
                        ...prev,
                        showPassword: !prev.showPassword,
                      }))
                    }
                  >
                    {state.showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              <div className="flex gap-2">
                <Button
                  type="submit"
                  className="flex-1"
                  disabled={state.isGenerating}
                >
                  {state.isGenerating ? (
                    <>
                      <QrCode className="mr-2 h-4 w-4 animate-spin" />
                      Generating QR Codes...
                    </>
                  ) : (
                    <>
                      <QrCode className="mr-2 h-4 w-4" />
                      Generate QR Codes
                    </>
                  )}
                </Button>
                {(state.wifiQRCode || state.uploadQRCode) && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={clearQRCodes}
                    disabled={state.isGenerating}
                  >
                    Clear
                  </Button>
                )}
              </div>
            </form>
          </CardContent>
        </Card>

        {/* QR Code Results */}
        <div className="space-y-6">
          {/* WiFi QR Code */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wifi className="h-5 w-5" />
                WiFi Connection QR Code
              </CardTitle>
              <CardDescription>
                Scan this QR code to automatically connect to the WiFi network
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              {state.wifiQRCode ? (
                <div className="space-y-4">
                  <img
                    src={state.wifiQRCode}
                    alt="WiFi QR Code"
                    className="mx-auto border rounded-lg"
                  />
                  <Button
                    onClick={() =>
                      downloadQRCode(state.wifiQRCode, "wifi-qr-code.png")
                    }
                    variant="outline"
                    className="w-full"
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Download WiFi QR Code
                  </Button>
                </div>
              ) : (
                <div className="py-12 text-muted-foreground">
                  <QrCode className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>WiFi QR code will appear here after generation</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Upload QR Code */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Document Upload QR Code
              </CardTitle>
              <CardDescription>
                Scan this QR code to access the document upload page
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              {state.uploadQRCode ? (
                <div className="space-y-4">
                  <img
                    src={state.uploadQRCode}
                    alt="Upload QR Code"
                    className="mx-auto border rounded-lg"
                  />
                  <Button
                    onClick={() =>
                      downloadQRCode(state.uploadQRCode, "upload-qr-code.png")
                    }
                    variant="outline"
                    className="w-full"
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Download Upload QR Code
                  </Button>
                </div>
              ) : (
                <div className="py-12 text-muted-foreground">
                  <Upload className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Upload QR code will appear here after generation</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
