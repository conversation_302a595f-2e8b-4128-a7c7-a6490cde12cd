/**
 * Test script for QR code generation functionality
 * Tests the QR code generation logic used in the QR access page
 */

const QRCode = require("qrcode");

async function testQRGeneration() {
  console.log("🧪 Testing QR Code Generation...\n");

  try {
    // Test 1: WiFi QR Code with password (WPA)
    console.log("📶 Test 1: WiFi QR Code with password");
    const wifiSSID = "TestNetwork";
    const wifiPassword = "testpassword123";
    const wifiString = `WIFI:T:WPA;S:${wifiSSID};P:${wifiPassword};H:false;;`;

    const wifiQRDataURL = await QRCode.toDataURL(wifiString, {
      width: 256,
      margin: 2,
      color: {
        dark: "#000000",
        light: "#FFFFFF",
      },
    });

    console.log(`✅ WiFi QR Code generated successfully`);
    console.log(`   SSID: ${wifiSSID}`);
    console.log(`   Security: WPA`);
    console.log(`   Data URL length: ${wifiQRDataURL.length} characters`);
    console.log(`   WiFi String: ${wifiString}\n`);

    // Test 2: WiFi QR Code without password (Open network)
    console.log("📶 Test 2: WiFi QR Code without password (Open network)");
    const openWifiSSID = "OpenNetwork";
    const openWifiString = `WIFI:T:nopass;S:${openWifiSSID};P:;H:false;;`;

    const openWifiQRDataURL = await QRCode.toDataURL(openWifiString, {
      width: 256,
      margin: 2,
      color: {
        dark: "#000000",
        light: "#FFFFFF",
      },
    });

    console.log(`✅ Open WiFi QR Code generated successfully`);
    console.log(`   SSID: ${openWifiSSID}`);
    console.log(`   Security: Open (no password)`);
    console.log(`   Data URL length: ${openWifiQRDataURL.length} characters`);
    console.log(`   WiFi String: ${openWifiString}\n`);

    // Test 3: Upload Document QR Code with localhost
    console.log("📤 Test 3: Upload Document QR Code (localhost)");
    const uploadURL = "http://localhost:3000/upload";

    const uploadQRDataURL = await QRCode.toDataURL(uploadURL, {
      width: 256,
      margin: 2,
      color: {
        dark: "#000000",
        light: "#FFFFFF",
      },
    });

    console.log(`✅ Upload QR Code generated successfully`);
    console.log(`   URL: ${uploadURL}`);
    console.log(`   Data URL length: ${uploadQRDataURL.length} characters\n`);

    // Test 3b: Upload Document QR Code with IP address
    console.log("📤 Test 3b: Upload Document QR Code (IP address)");
    const ipUploadURL = "http://*************:3000/upload";

    const ipUploadQRDataURL = await QRCode.toDataURL(ipUploadURL, {
      width: 256,
      margin: 2,
      color: {
        dark: "#000000",
        light: "#FFFFFF",
      },
    });

    console.log(`✅ IP Upload QR Code generated successfully`);
    console.log(`   URL: ${ipUploadURL}`);
    console.log(`   Data URL length: ${ipUploadQRDataURL.length} characters\n`);

    // Test 4: Error handling - Invalid characters
    console.log("❌ Test 4: Error handling with special characters");
    try {
      const specialSSID = "Test;Network:With\"Special'Chars";
      const specialWifiString = `WIFI:T:WPA;S:${specialSSID};P:password;H:false;;`;

      const specialQRDataURL = await QRCode.toDataURL(specialWifiString, {
        width: 256,
        margin: 2,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
      });

      console.log(`✅ Special characters handled successfully`);
      console.log(`   SSID: ${specialSSID}`);
      console.log(
        `   Data URL length: ${specialQRDataURL.length} characters\n`
      );
    } catch (error) {
      console.log(`⚠️  Special characters caused error: ${error.message}\n`);
    }

    // Test 5: Very long SSID and password
    console.log("📏 Test 5: Long SSID and password");
    const longSSID = "A".repeat(32); // Maximum SSID length
    const longPassword = "B".repeat(63); // Maximum WPA password length
    const longWifiString = `WIFI:T:WPA;S:${longSSID};P:${longPassword};H:false;;`;

    const longQRDataURL = await QRCode.toDataURL(longWifiString, {
      width: 256,
      margin: 2,
      color: {
        dark: "#000000",
        light: "#FFFFFF",
      },
    });

    console.log(`✅ Long SSID and password handled successfully`);
    console.log(`   SSID length: ${longSSID.length} characters`);
    console.log(`   Password length: ${longPassword.length} characters`);
    console.log(`   Data URL length: ${longQRDataURL.length} characters\n`);

    console.log("🎉 All QR code generation tests passed!");
    console.log("\n📋 Summary:");
    console.log("   ✅ WiFi QR Code with password (WPA)");
    console.log("   ✅ WiFi QR Code without password (Open)");
    console.log("   ✅ Upload Document QR Code (localhost)");
    console.log("   ✅ Upload Document QR Code (IP address)");
    console.log("   ✅ Special characters handling");
    console.log("   ✅ Long SSID and password handling");
  } catch (error) {
    console.error("❌ QR code generation test failed:", error);
    process.exit(1);
  }
}

// Run the test
testQRGeneration();
