# QR Access Generator

The Legal Document Issuance System (LDIS) includes a QR Access Generator that creates QR codes for WiFi connection and document upload access. This feature is designed to provide easy access to the system for users in physical locations.

## Overview

The QR Access Generator provides:

- **WiFi Connection QR Code** - Automatically connects users to a specified WiFi network
- **Document Upload QR Code** - Provides direct access to the document upload page
- **Download Functionality** - Allows downloading QR codes as PNG images
- **Security Support** - Handles both WPA/WPA2 secured and open WiFi networks
- **User-friendly Interface** - Clean, modern interface using Shadcn components

## Features

### QR Access Page (`/qr`)

- **WiFi Configuration Form** - Input fields for network name (SSID) and password
- **Password Visibility Toggle** - Show/hide password for easier input
- **Dual QR Code Generation** - Creates both WiFi and upload QR codes simultaneously
- **Download Options** - Individual download buttons for each QR code
- **Clear Functionality** - Reset form and clear generated QR codes
- **Error Handling** - Comprehensive validation and error reporting
- **Responsive Design** - Works on desktop and mobile devices

### WiFi QR Code

The WiFi QR code follows the standard WiFi QR format:

- **WPA/WPA2 Networks**: `WIFI:T:WPA;S:SSID;P:password;H:false;;`
- **Open Networks**: `WIFI:T:nopass;S:SSID;P:;H:false;;`

Supported features:

- Automatic security type detection based on password presence
- Support for special characters in SSID and password
- Maximum SSID length: 32 characters
- Maximum WPA password length: 63 characters

### Upload QR Code

The upload QR code contains a direct URL to the document upload page:

- Format: `http://[IP_ADDRESS]:[PORT]/upload`
- **Automatic IP Detection**: Uses WebRTC to detect local network IP address
- **Fallback Methods**: Falls back to hostname or common IP patterns if detection fails
- Works with both development and production environments

#### IP Detection Methods

1. **WebRTC Detection**: Uses WebRTC peer connection to discover actual local IP addresses (192.168.x.x, 10.x.x.x, 172.16-31.x.x)
2. **Hostname Analysis**: Checks if current hostname is already an IP address
3. **Localhost Warning**: Shows warning message when running on localhost, advising to use actual IP address

## File Structure

```
src/app/qr/page.tsx                    # QR Access page component
scripts/test-qr-generation.js          # QR generation testing script
docs/QR_ACCESS.md                      # This documentation
```

## Usage Examples

### Generating QR Codes

1. Navigate to `/qr` page (accessible via Admin sidebar)
2. Enter WiFi network name (SSID) - required
3. Enter WiFi password (optional for open networks)
4. Click "Generate QR Codes"
5. Download individual QR codes as needed

### Using WiFi QR Code

1. Open camera app on mobile device
2. Point camera at WiFi QR code
3. Tap the notification to connect to WiFi
4. Device automatically connects using provided credentials

### Using Upload QR Code

1. Open camera app on mobile device
2. Point camera at Upload QR code
3. Tap the notification to open the link
4. Browser opens directly to the document upload page

## API Integration

The QR Access page uses the `qrcode` library for QR code generation:

```javascript
import QRCode from "qrcode";

// Generate WiFi QR Code
const wifiString = `WIFI:T:WPA;S:${ssid};P:${password};H:false;;`;
const qrDataURL = await QRCode.toDataURL(wifiString, {
  width: 256,
  margin: 2,
  color: {
    dark: "#000000",
    light: "#FFFFFF",
  },
});
```

## Testing

### Automated Testing

Run the QR generation test suite:

```bash
pnpm qr:test
```

This will test:

1. WiFi QR Code with password (WPA)
2. WiFi QR Code without password (Open network)
3. Upload Document QR Code generation
4. Special characters handling
5. Long SSID and password handling

### Manual Testing

1. Start the development server: `pnpm dev`
2. Navigate to `http://localhost:3000/qr`
3. Test WiFi QR code generation with various network configurations
4. Test upload QR code generation
5. Verify QR codes work with mobile devices
6. Test download functionality

## Security Considerations

### WiFi Security

- **Password Visibility**: Password field is masked by default with toggle option
- **No Server Storage**: WiFi credentials are not stored on the server
- **Client-side Generation**: QR codes are generated entirely in the browser
- **Temporary Data**: Form data is cleared when QR codes are cleared

### Access Control

- **Admin Mode Required**: QR Access page is only accessible when admin mode is enabled
- **Authentication Protected**: Requires admin authentication to access
- **No Persistent Storage**: Generated QR codes are not saved to database

## Technical Notes

### QR Code Generation

- Uses `qrcode` library version 1.5.4
- Generates data URLs for immediate display
- 256x256 pixel resolution with 2-pixel margin
- Black and white color scheme for maximum compatibility

### WiFi QR Format

The system follows the standard WiFi QR code format:

- `T`: Security type (WPA, WEP, nopass)
- `S`: SSID (network name)
- `P`: Password
- `H`: Hidden network flag (always false)

### Browser Compatibility

- **Modern Browsers**: Full support for Chrome, Firefox, Safari, Edge
- **Mobile Devices**: Optimized for iOS and Android camera apps
- **QR Code Scanning**: Compatible with built-in camera apps and QR scanner apps

## Performance Considerations

### Client-side Processing

- QR code generation happens entirely in the browser
- No server requests for QR generation
- Immediate feedback and generation
- Minimal bandwidth usage

### Image Optimization

- Data URLs are generated for immediate display
- PNG format for download compatibility
- Optimized size (256x256) for scanning reliability
- Minimal file sizes for quick downloads

## Troubleshooting

### Common Issues

1. **QR Code Not Scanning**

   - Ensure adequate lighting when scanning
   - Try different QR scanner apps
   - Check for special characters in SSID/password

2. **WiFi Connection Fails**

   - Verify SSID and password are correct
   - Check WiFi network is broadcasting
   - Ensure device supports WPA/WPA2

3. **Upload QR Code Not Working**
   - Check network connectivity
   - Verify the LDIS server is running
   - Try opening the URL manually

### Error Messages

- **"WiFi network name is required"**: SSID field cannot be empty
- **"Failed to generate QR codes"**: Check browser console for detailed errors
- **"Failed to download QR code"**: Browser may be blocking downloads

## Future Enhancements

Potential improvements for the QR Access system:

1. **Multiple Security Types**: Support for WEP and enterprise networks
2. **Batch Generation**: Generate multiple QR codes at once
3. **Custom Styling**: Branded QR codes with logos
4. **Print Layout**: Optimized layout for printing QR codes
5. **QR Code History**: Save and manage previously generated codes
6. **Analytics**: Track QR code usage and scanning statistics
